'use client'

import { useFormBuilderStore } from '@/store/formBuilderStore'
import FieldPalette from './FormBuilder/FieldPalette'
import FormCanvas from './FormBuilder/FormCanvas'
import FormPreview from './FormBuilder/FormPreview'

export default function FormBuilder() {
  const { isPreviewMode } = useFormBuilderStore()

  if (isPreviewMode) {
    return <FormPreview />
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 min-h-[600px]">
      {/* Field Palette */}
      <div className="lg:col-span-1">
        <FieldPalette />
      </div>
      
      {/* Form Canvas */}
      <div className="lg:col-span-3">
        <FormCanvas />
      </div>
    </div>
  )
}

'use client'

import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { FieldConfig } from '@/types/form'
import { useFormBuilderStore } from '@/store/formBuilderStore'
import {
  GripVertical,
  Settings,
  Copy,
  Trash2,
  Type,
  AlignLeft,
  Hash,
  Calendar,
  ChevronDown,
  CheckSquare,
  Circle,
  Upload,
  List
} from 'lucide-react'
import { useState } from 'react'

interface SortableFieldItemProps {
  field: FieldConfig
}

const fieldIcons = {
  text: <Type size={16} />,
  textarea: <AlignLeft size={16} />,
  number: <Hash size={16} />,
  date: <Calendar size={16} />,
  dropdown: <ChevronDown size={16} />,
  multiselect: <List size={16} />,
  checkbox: <CheckSquare size={16} />,
  radio: <Circle size={16} />,
  file: <Upload size={16} />,
}

export default function SortableFieldItem({ field }: SortableFieldItemProps) {
  const [showConfig, setShowConfig] = useState(false)
  const { updateField, removeField, duplicateField } = useFormBuilderStore()

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: field.id,
    data: {
      type: 'field',
      field,
    },
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const handleUpdateField = (updates: Partial<FieldConfig>) => {
    updateField(field.id, updates)
  }

  const handleRemoveField = () => {
    removeField(field.id)
  }

  const handleDuplicateField = () => {
    duplicateField(field.id)
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        card bg-base-100 border border-base-300 shadow-sm
        ${isDragging ? 'opacity-50' : ''}
        ${showConfig ? 'ring-2 ring-primary' : ''}
      `}
    >
      <div className="card-body p-4">
        {/* Field Header */}
        <div className="flex items-center gap-3">
          {/* Drag Handle */}
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing text-base-content/50 hover:text-base-content"
          >
            <GripVertical size={16} />
          </div>

          {/* Field Icon */}
          <div className="text-primary">
            {fieldIcons[field.type]}
          </div>

          {/* Field Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-sm">
                {field.label || `${field.type} field`}
              </h3>
              {field.validation?.required && (
                <span className="badge badge-error badge-xs">Required</span>
              )}
            </div>
            <p className="text-xs text-base-content/60 capitalize">
              {field.type} field
            </p>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowConfig(!showConfig)}
              className={`btn btn-ghost btn-xs ${showConfig ? 'btn-active' : ''}`}
              title="Configure field"
            >
              <Settings size={14} />
            </button>
            <button
              onClick={handleDuplicateField}
              className="btn btn-ghost btn-xs"
              title="Duplicate field"
            >
              <Copy size={14} />
            </button>
            <button
              onClick={handleRemoveField}
              className="btn btn-ghost btn-xs text-error hover:bg-error hover:text-error-content"
              title="Remove field"
            >
              <Trash2 size={14} />
            </button>
          </div>
        </div>

        {/* Field Configuration */}
        {showConfig && (
          <div className="mt-4 pt-4 border-t border-base-300">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Label */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text text-xs">Label</span>
                </label>
                <input
                  type="text"
                  placeholder="Field label"
                  className="input input-bordered input-sm"
                  value={field.label || ''}
                  onChange={(e) => handleUpdateField({ label: e.target.value })}
                />
              </div>

              {/* Placeholder */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text text-xs">Placeholder</span>
                </label>
                <input
                  type="text"
                  placeholder="Placeholder text"
                  className="input input-bordered input-sm"
                  value={field.placeholder || ''}
                  onChange={(e) => handleUpdateField({ placeholder: e.target.value })}
                />
              </div>

              {/* Required */}
              <div className="form-control">
                <label className="cursor-pointer label">
                  <span className="label-text text-xs">Required</span>
                  <input
                    type="checkbox"
                    className="toggle toggle-primary toggle-sm"
                    checked={field.validation?.required || false}
                    onChange={(e) => handleUpdateField({ 
                      validation: { 
                        ...field.validation, 
                        required: e.target.checked 
                      } 
                    })}
                  />
                </label>
              </div>

              {/* Options for dropdown, multiselect, radio */}
              {(field.type === 'dropdown' || field.type === 'multiselect' || field.type === 'radio') && (
                <div className="form-control md:col-span-2">
                  <label className="label">
                    <span className="label-text text-xs">Options (one per line)</span>
                  </label>
                  <textarea
                    placeholder="Option 1&#10;Option 2&#10;Option 3"
                    className="textarea textarea-bordered textarea-sm"
                    rows={3}
                    value={field.options?.map(opt => opt.label).join('\n') || ''}
                    onChange={(e) => {
                      const lines = e.target.value.split('\n').filter(line => line.trim())
                      const options = lines.map(line => ({
                        label: line.trim(),
                        value: line.trim().toLowerCase().replace(/\s+/g, '-')
                      }))
                      handleUpdateField({ options })
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

'use client'

import { useFormBuilderStore } from '@/store/formBuilderStore'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FieldConfig } from '@/types/form'

// Generate dynamic schema based on form fields
function generateFormSchema(fields: FieldConfig[]) {
  const schemaFields: Record<string, z.ZodTypeAny> = {}

  fields.forEach(field => {
    let fieldSchema: z.ZodTypeAny

    switch (field.type) {
      case 'text':
      case 'textarea':
        fieldSchema = z.string()
        if (field.validation?.minLength) {
          fieldSchema = fieldSchema.min(field.validation.minLength)
        }
        if (field.validation?.maxLength) {
          fieldSchema = fieldSchema.max(field.validation.maxLength)
        }
        if (field.validation?.pattern) {
          fieldSchema = fieldSchema.regex(new RegExp(field.validation.pattern))
        }
        break
      case 'number':
        fieldSchema = z.number()
        if (field.validation?.min !== undefined) {
          fieldSchema = fieldSchema.min(field.validation.min)
        }
        if (field.validation?.max !== undefined) {
          fieldSchema = fieldSchema.max(field.validation.max)
        }
        break
      case 'date':
        fieldSchema = z.string().refine(val => !isNaN(Date.parse(val)), {
          message: 'Invalid date format'
        })
        break
      case 'dropdown':
      case 'radio':
        if (field.options && field.options.length > 0) {
          const validValues = field.options.map(opt => opt.value)
          fieldSchema = z.enum(validValues as [string, ...string[]])
        } else {
          fieldSchema = z.string()
        }
        break
      case 'checkbox':
        if (field.options && field.options.length > 0) {
          fieldSchema = z.array(z.string())
        } else {
          fieldSchema = z.boolean()
        }
        break
      case 'file':
        fieldSchema = z.any() // File handling would need special treatment
        break
      default:
        fieldSchema = z.string()
    }

    if (!field.validation?.required) {
      fieldSchema = fieldSchema.optional()
    }

    schemaFields[field.id] = fieldSchema
  })

  return z.object(schemaFields)
}

function renderField(field: FieldConfig, register: any, errors: any) {
  const hasError = errors[field.id]
  const baseClasses = `input input-bordered w-full ${hasError ? 'input-error' : ''}`

  switch (field.type) {
    case 'text':
      return (
        <input
          type="text"
          placeholder={field.placeholder}
          className={baseClasses}
          {...register(field.id)}
        />
      )
    
    case 'textarea':
      return (
        <textarea
          placeholder={field.placeholder}
          className={`textarea textarea-bordered w-full ${hasError ? 'textarea-error' : ''}`}
          rows={4}
          {...register(field.id)}
        />
      )
    
    case 'number':
      return (
        <input
          type="number"
          placeholder={field.placeholder}
          className={baseClasses}
          {...register(field.id, { valueAsNumber: true })}
        />
      )
    
    case 'date':
      return (
        <input
          type="date"
          className={baseClasses}
          {...register(field.id)}
        />
      )
    
    case 'dropdown':
      return (
        <select
          className={`select select-bordered w-full ${hasError ? 'select-error' : ''}`}
          {...register(field.id)}
        >
          <option value="">Select an option</option>
          {field.options?.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )
    
    case 'radio':
      return (
        <div className="space-y-2">
          {field.options?.map((option) => (
            <label key={option.value} className="cursor-pointer label justify-start gap-3">
              <input
                type="radio"
                value={option.value}
                className="radio radio-primary"
                {...register(field.id)}
              />
              <span className="label-text">{option.label}</span>
            </label>
          ))}
        </div>
      )
    
    case 'checkbox':
      if (field.options && field.options.length > 1) {
        return (
          <div className="space-y-2">
            {field.options.map((option) => (
              <label key={option.value} className="cursor-pointer label justify-start gap-3">
                <input
                  type="checkbox"
                  value={option.value}
                  className="checkbox checkbox-primary"
                  {...register(field.id)}
                />
                <span className="label-text">{option.label}</span>
              </label>
            ))}
          </div>
        )
      } else {
        return (
          <label className="cursor-pointer label justify-start gap-3">
            <input
              type="checkbox"
              className="checkbox checkbox-primary"
              {...register(field.id)}
            />
            <span className="label-text">{field.label}</span>
          </label>
        )
      }
    
    case 'file':
      return (
        <input
          type="file"
          className={`file-input file-input-bordered w-full ${hasError ? 'file-input-error' : ''}`}
          {...register(field.id)}
        />
      )
    
    default:
      return (
        <input
          type="text"
          placeholder={field.placeholder}
          className={baseClasses}
          {...register(field.id)}
        />
      )
  }
}

export default function FormPreview() {
  const { currentForm } = useFormBuilderStore()
  const fields = currentForm?.fields || []

  const schema = generateFormSchema(fields)
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
  })

  const onSubmit = (data: any) => {
    console.log('Form submission:', data)
    // In a real app, this would submit to the API
    alert('Form submitted successfully! Check console for data.')
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold">
              {currentForm?.title || 'Form Preview'}
            </h1>
            {currentForm?.description && (
              <p className="text-base-content/70 mt-2">
                {currentForm.description}
              </p>
            )}
            <div className="badge badge-info mt-4">Preview Mode</div>
          </div>

          {fields.length === 0 ? (
            <div className="text-center py-12 text-base-content/50">
              <p>No fields added yet. Switch back to builder mode to add fields.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {fields.map((field) => (
                <div key={field.id} className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">
                      {field.label}
                      {field.validation?.required && (
                        <span className="text-error ml-1">*</span>
                      )}
                    </span>
                  </label>
                  
                  {renderField(field, register, errors)}
                  
                  {errors[field.id] && (
                    <label className="label">
                      <span className="label-text-alt text-error">
                        {errors[field.id]?.message as string}
                      </span>
                    </label>
                  )}
                </div>
              ))}

              <div className="form-control mt-8">
                <button type="submit" className="btn btn-primary">
                  Submit Form
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
